import React, { createContext, useContext, useState, useMemo, useCallback } from 'react'

const VideoToastContext = createContext()

export const useVideoToastContext = () => {
  const context = useContext(VideoToastContext)
  if (!context) {
    throw new Error('useVideoToastContext must be used within a VideoToastProvider')
  }
  return context
}

export function VideoToastProvider({ children }) {
  // Toast State
  const [toastNotification, setToastNotification] = useState("")
  const [showToast, setShowToast] = useState(false)
  const [toastStatus, setToastStatus] = useState("")

  // Helper function with stable reference
  const showToastMessage = useCallback((message, status = "info") => {
    setToastNotification(message)
    setToastStatus(status)
    setShowToast(true)
  }, [])

  const value = useMemo(() => ({
    // Toast State
    toastNotification,
    showToast,
    toastStatus,
    
    // Toast Actions
    setToastNotification,
    setShowToast,
    setToastStatus,
    showToastMessage,
  }), [toastNotification, showToast, toastStatus, showToastMessage])

  return (
    <VideoToastContext.Provider value={value}>
      {children}
    </VideoToastContext.Provider>
  )
}

export default VideoToastContext

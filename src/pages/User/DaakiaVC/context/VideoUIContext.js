import React, { createContext, useContext, useState, useMemo } from 'react'

const VideoUIContext = createContext()

export const useVideoUIContext = () => {
  const context = useContext(VideoUIContext)
  if (!context) {
    throw new Error('useVideoUIContext must be used within a VideoUIProvider')
  }
  return context
}

export function VideoUIProvider({ children }) {
  // UI State - Drawer Management
  const [showParticipantsList, setShowParticipantsList] = useState(false)

  const value = useMemo(() => ({
    // UI State
    showParticipantsList,
    setShowParticipantsList,
  }), [showParticipantsList])

  return (
    <VideoUIContext.Provider value={value}>
      {children}
    </VideoUIContext.Provider>
  )
}

export default VideoUIContext

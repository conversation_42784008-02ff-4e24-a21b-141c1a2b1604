import React, { createContext, useContext, useState, useMemo } from 'react'

const VideoConferencesContext = createContext()

export const useVideoConferencesContext = () => {
  const context = useContext(VideoConferencesContext)
  if (!context) {
    throw new Error('useVideoConferencesContext must be used within a VideoConferencesProvider')
  }
  return context
}

export function VideoConferencesProvider({ children }) {
  // UI State - Drawer Management
  const [showParticipantsList, setShowParticipantsList] = useState(false)

  const value = useMemo(() => ({
    showParticipantsList,
    setShowParticipantsList,
  }), [showParticipantsList])

  return (
    <VideoConferencesContext.Provider value={value}>
      {children}
    </VideoConferencesContext.Provider>
  )
}

export default VideoConferencesContext
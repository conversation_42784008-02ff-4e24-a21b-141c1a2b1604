import React from 'react'
import { VideoUIProvider, useVideoUIContext } from './VideoUIContext'
import { VideoToastProvider, useVideoToastContext } from './VideoToastContext'

export function VideoConferencesProvider({ children }) {
  return (
    <VideoUIProvider>
      <VideoToastProvider>
        {children}
      </VideoToastProvider>
    </VideoUIProvider>
  )
}


export { useVideoUIContext } from './VideoUIContext'
export { useVideoToastContext } from './VideoToastContext'

export const useVideoConferencesContext = () => {
  const uiContext = useVideoUIContext()
  const toastContext = useVideoToastContext()

  return {
    ...uiContext,
    ...toastContext,
  }
}

export default VideoConferencesProvider
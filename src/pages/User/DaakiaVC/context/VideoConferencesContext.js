import React, { createContext, useContext, useState, useMemo, useCallback } from 'react'

const VideoConferencesContext = createContext()

export const useVideoConferencesContext = () => {
  const context = useContext(VideoConferencesContext)
  if (!context) {
    throw new Error('useVideoConferencesContext must be used within a VideoConferencesProvider')
  }
  return context
}



export function VideoConferencesProvider({ children }) {
    // UI State
    const [showParticipantsList, setShowParticipantsList] = useState(false)
    
    // Toast State (separate from UI state)
    const [toastNotification, setToastNotification] = useState("")
    const [showToast, setShowToast] = useState(false)
    const [toastStatus, setToastStatus] = useState("")
  
    // Helper function (stable reference)
    const showToastMessage = useCallback((message, status = "info") => {
      setToastNotification(message)
      setToastStatus(status)
      setShowToast(true)
    }, [])
  
    // Split into two separate memos to prevent unnecessary re-renders
    const uiValue = useMemo(() => ({
      showParticipantsList,
      setShowParticipantsList,
    }), [showParticipantsList])
  
    const toastValue = useMemo(() => ({
      toastNotification,
      showToast,
      toastStatus,
      setToastNotification,
      setShowToast,
      setToastStatus,
      showToastMessage,
    }), [toastNotification, showToast, toastStatus, showToastMessage])
  
    const value = useMemo(() => ({
      ...uiValue,
      ...toastValue,
    }), [uiValue, toastValue])
  
    return (
      <VideoConferencesContext.Provider value={value}>
        {children}
      </VideoConferencesContext.Provider>
    )
  }

  export default VideoConferencesContext;
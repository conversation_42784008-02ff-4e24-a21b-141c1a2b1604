import React from 'react'
import { VideoUIProvider, useVideoUIContext } from './VideoUIContext'
import { VideoToastProvider, useVideoToastContext } from './VideoToastContext'

// Combined provider that wraps both UI and Toast contexts
export function VideoConferencesProvider({ children }) {
  return (
    <VideoUIProvider>
      <VideoToastProvider>
        {children}
      </VideoToastProvider>
    </VideoUIProvider>
  )
}

// Re-export individual context hooks for convenience
export { useVideoUIContext } from './VideoUIContext'
export { useVideoToastContext } from './VideoToastContext'

// Legacy export for backward compatibility (combines both contexts)
// NOTE: Use individual hooks (useVideoUIContext, useVideoToastContext) for better performance
export const useVideoConferencesContext = () => {
  const uiContext = useVideoUIContext()
  const toastContext = useVideoToastContext()

  return {
    ...uiContext,
    ...toastContext,
  }
}

export default VideoConferencesProvider
import React, { createContext, useContext, useState } from 'react'

const VideoConferencesContext = createContext()

export const useVideoConferencesContext = () => {
  const context = useContext(VideoConferencesContext)
  if (!context) {
    throw new Error('useVideoConferencesContext must be used within a VideoConferencesProvider')
  }
  return context
}

export const VideoConferencesProvider = ({ children }) => {
  const [exampleState, setExampleState] = useState(null)

  const value = {
    exampleState,
    setExampleState,
  }

  return (
    <VideoConferencesContext.Provider value={value}>
      {children}
    </VideoConferencesContext.Provider>
  )
}

export default VideoConferencesContext
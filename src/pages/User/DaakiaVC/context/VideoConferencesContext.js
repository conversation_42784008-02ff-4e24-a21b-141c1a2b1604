import React, { createContext, useContext, useState, useMemo } from 'react'

const VideoConferencesContext = createContext()

export const useVideoConferencesContext = () => {
  const context = useContext(VideoConferencesContext)
  if (!context) {
    throw new Error('useVideoConferencesContext must be used within a VideoConferencesProvider')
  }
  return context
}

export function VideoConferencesProvider({ children }) {
  const [exampleState, setExampleState] = useState(null)

  const value = useMemo(() => ({
    exampleState,
    setExampleState,
  }), [exampleState])

  return (
    <VideoConferencesContext.Provider value={value}>
      {children}
    </VideoConferencesContext.Provider>
  )
}

export default VideoConferencesContext
import React, { createContext, useContext, useState } from 'react'

const IndexContext = createContext()

export const useIndexContext = () => {
  const context = useContext(IndexContext)
  if (!context) {
    throw new Error('useIndexContext must be used within an IndexProvider')
  }
  return context
}
export const IndexProvider = ({ children }) => {
  const [exampleState, setExampleState] = useState(null)
  const value = {
    exampleState,
    setExampleState,
  }

  return (
    <IndexContext.Provider value={value}>
      {children}
    </IndexContext.Provider>
  )
}

export default IndexContext
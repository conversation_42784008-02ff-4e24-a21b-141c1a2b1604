import React from "react";

import { DrawerState } from "../../utils/constants";
import "../../styles/ControlBar.scss";
import "../../styles/Settings.scss";
import "../../styles/index.scss";
import { ReactComponent as SvgParticipantIcon } from "./icons/ParticipantIcon.svg";
import { ReactComponent as SvgParticipantBlueIcon } from "./icons/ParticpantOnIcon.svg";

// Context import
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";

export function ParticipantControlButton({
  setDrawerState,
}) {
  // Use context instead of props
  const { showParticipantsList, setShowParticipantsList } = useVideoConferencesContext();
  return (
    <div
      onClick={() => {
        setShowParticipantsList(!showParticipantsList);
        setDrawerState(DrawerState.PARTICIPANTS);
      }}
      className="lk-button control-bar-button control-bar-button-icon participants-icon lk-button-group-buttons"
    >
      {showParticipantsList ? (
        <SvgParticipantBlueIcon />
      ) : (
        <SvgParticipantIcon />
      )}
    </div>
  );
}
